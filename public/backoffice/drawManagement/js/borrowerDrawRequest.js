/**
 * BorrowerDrawRequestManager Class
 * Manages draw request functionality for borrowers including note management,
 * validation, form submission, and API integration
 */
class BorrowerDrawRequestManager {
    constructor(config = {}) {
        this.config = {
            $saveBtn: config.$saveBtn || $('#btnSave'),
            LMRId: config.LMRId || null,
            status: config.status || 'pending',
            validationContext: config.validationContext || {
                userType: 'borrower',
                isDraft: false,
                isDrawRequest: true
            }
        };

        this.noteState = {
            currentNoteBtn: null
        };

        this.init();
    }

    /**
     * Initialize the borrower draw request manager
     */
    init() {
        this.setupEventHandlers();
        this.initializeNoteButtons();
        this.validateSubmitButton();
        this.initializeAmountBadges();
    }

    /**
     * Set up all event handlers
     */
    setupEventHandlers() {
        this.setupNoteModalHandlers();
        var self = this;
        this.config.$saveBtn.on('click', async (e) => {
            e.preventDefault();
            $.confirm({
                icon: 'fa fa-warning',
                closeIcon: true,
                title: 'Confirm',
                content: 'Are you sure you want to submit the draw request?',
                type: 'green',
                backgroundDismiss: true,
                buttons: {
                    yes: {
                        text: 'Yes',
                        action: function () {
                            self.submitDrawRequest();
                        }
                    },
                    cancel: {
                        text: 'Cancel',
                        action: function () {
                        }
                    },
                },
                onClose: function () {
                },
            });
        });

        DrawRequestUtils.setupInputHandlers(() => this.validateSubmitButton());
    }

    /**
     * Set up note modal event handlers
     */
    setupNoteModalHandlers() {
        // Remove any existing handlers to prevent duplicates
        $('#noteModal').off('show.bs.modal.borrowerDrawRequest');
        $('#saveNoteBtn').off('click.borrowerDrawRequest');

        $('#noteModal').on('show.bs.modal.borrowerDrawRequest', (event) => {
            this.noteState.currentNoteBtn = $(event.relatedTarget);
            const noteText = this.noteState.currentNoteBtn.data('note') || '';
            $('#noteTextarea').val(noteText);
        });

        $('#saveNoteBtn').on('click.borrowerDrawRequest', () => {
            this.saveNote();
        });
    }

    /**
     * Initialize note buttons with proper styling
     */
    initializeNoteButtons() {
        $('.note-btn').each(function() {
            const noteText = $(this).data('note') || '';
            const icon = $(this).find('i');
            if (noteText.trim()) {
                icon.addClass('text-primary');
            } else {
                icon.addClass('text-muted');
            }
        });
    }

    /**
     * Initialize amount badges with current values
     */
    initializeAmountBadges() {
        if (typeof DrawRequestUtils !== 'undefined' && typeof DrawRequestUtils.updateAmountBadges === 'function') {
            DrawRequestUtils.updateAmountBadges();
        }
    }

    /**
     * Save note and update UI
     */
    saveNote() {
        console.log('BorrowerDrawRequestManager.saveNote() called');
        const updatedNote = $('#noteTextarea').val();
        console.log('Updated note:', updatedNote);

        if (this.noteState.currentNoteBtn) {
            this.noteState.currentNoteBtn.data('note', updatedNote);
            this.noteState.currentNoteBtn.attr('data-note', updatedNote);
            this.noteState.currentNoteBtn.find('i').attr('data-original-title', updatedNote);

            const icon = this.noteState.currentNoteBtn.find('i');
            if (updatedNote.trim()) {
                icon.removeClass('text-muted').addClass('text-primary');
            } else {
                icon.removeClass('text-primary').addClass('text-muted');
            }

            $('#noteModal').modal('hide');
            setTimeout(function() {
                $('.modal-backdrop').remove();
                $('body').removeClass('modal-open');
            }, 150);

            if (typeof DrawManagement !== 'undefined') {
                DrawManagement.config.lineItemsModified = true;
            }

            console.log('Note saved successfully');
        } else {
            console.error('No current note button found');
        }
    }

    /**
     * Validate submit button state
     */
    validateSubmitButton() {
        const hasValidationErrors = DrawRequestUtils.hasValidationErrors();
        this.config.$saveBtn.prop('disabled', hasValidationErrors);
    }

    /**
     * Collect line items data from the form
     * @returns {Object} Object containing line items data
     */
    collectLineItemsData() {
        const lineItemsData = {};

        $('.line-item').each(function() {
            const $row = $(this);
            const $amountInput = $row.find('.requested-amount');
            const $noteBtn = $row.find('.note-btn');

            if ($amountInput.length > 0) {
                const lineItemId = $amountInput.data('line-item-id');
                const requestedAmount = parseFloat($amountInput.val()) || 0;
                const notes = $noteBtn.data('note') || '';

                const lineItemData = {
                    id: lineItemId,
                    requestedAmount: requestedAmount,
                    notes: notes
                };

                lineItemsData[lineItemId] = DataMapper.mapObject(lineItemData, 'lineItem', 'toBackend');
            }
        });

        return lineItemsData;
    }

    /**
     * Create draw request data object
     * @returns {Object} Draw request data
     */
    createDrawRequestData() {
        const lineItemsData = this.collectLineItemsData();
        const LMRId = this.config.LMRId || $('#LMRId').val();

        return {
            LMRId: LMRId,
            status: this.config.status,
            lineItems: lineItemsData
        };
    }

    /**
     * Validate draw request data
     * @param {Object} drawRequestData - Data to validate
     * @returns {boolean} True if valid, false otherwise
     */
    validateDrawRequestData(drawRequestData) {
        const validator = new Validator();

        if (!validator.validateForm(drawRequestData, 'lineItems', this.config.validationContext)) {
            const errors = validator.getErrors();
            toastrNotification('Validation Error: ' + errors.join(', '), 'error');
            return false;
        }

        return true;
    }

    /**
     * Submit draw request
     */
    submitDrawRequest() {
        if (!DrawRequestUtils.validateAllInputs()) {
            toastrNotification('Please fix validation errors before submitting.', 'error');
            return;
        }

        const drawRequestData = this.createDrawRequestData();

        if (!this.validateDrawRequestData(drawRequestData)) {
            return;
        }

        this.sendDrawRequestData(drawRequestData);
    }

    /**
     * Send draw request data to API
     * @param {Object} drawRequestData - Data to send
     */
    sendDrawRequestData(drawRequestData) {
        const sanitizedData = DataMapper.sanitizeObject(drawRequestData);
        this.config.$saveBtn.prop('disabled', true).text('Submitting...');

        drawManagementApi.saveDrawRequest(sanitizedData)
            .then((response) => {
                this.handleSubmitResponse(response);
            })
            .catch((error) => {
                this.handleSubmitError(error);
            })
            .always(() => {
                this.config.$saveBtn.prop('disabled', false).text('Submit');
            });
    }

    /**
     * Handle successful submit response
     * @param {Object} response - API response
     */
    handleSubmitResponse(response) {
        if (response.success) {
            toastrNotification('Draw request submitted successfully!', 'success');
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            toastrNotification(response.message || 'Failed to submit draw request.', 'error');
        }
    }

    /**
     * Handle submit error
     * @param {Error} error - Error object
     */
    handleSubmitError(error) {
        console.error('API Error:', error);
        let errorMsg = error.message || 'An error occurred while submitting the draw request.';
        toastrNotification(errorMsg, 'error');
    }

    /**
     * Update configuration
     * @param {Object} newConfig - New configuration values
     */
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
    }

    /**
     * Get current configuration
     * @returns {Object} Current configuration
     */
    getConfig() {
        return { ...this.config };
    }

    /**
     * Get current note state
     * @returns {Object} Current note state
     */
    getNoteState() {
        return { ...this.noteState };
    }
}

