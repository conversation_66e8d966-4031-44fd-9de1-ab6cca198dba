DrawManagement.borrower = {

    init: function() {
        DrawManagement.config.LMRId = $('#LMRId').val();
        DrawManagement.config.dataKey = 'LMRId';
        DrawManagement.config.saveLineItemsSuccessMessage = 'Scope of Work Submitted!';
        DrawManagement.config.rehabCost = parseInt(DrawManagement.templateSettings.rehabCost);
        DrawManagement.config.isInitialScopeOfWork = DrawManagement.templateSettings.isInitialScopeOfWork;

        this.initEventHandlers();
        $('.percentage').each(function() {
            const percentage = parseInt($(this).text());
            $(this).addClass(DrawManagement.borrower.getPercentageColor(percentage));
        });
    },

    hasPermission: function(permission) {
        return DrawManagement.templateSettings[permission] === 1 && DrawManagement.currentTemplateData.status !== 'pending';
    },

    applyPermissions: function() {
        const self = this;

        if (!self.hasPermission('allowBorrowersAddEditCategories')) {
            DrawManagement.elements.$openAddCategoryModalBtn.remove();
            $('.edit-category-btn').remove();
        }

        if (!self.hasPermission('allowBorrowersDeleteCategories')) {
            $('.delete-category-btn').remove();
        }

        if (!self.hasPermission('allowBorrowersAddEditLineItems')) {
            $('.add-line-item-row').remove();
            $('.drag-handle-cell').remove();
            DrawManagement.elements.$lineItemCategoriesContainer.off('click', '.editable-line-item td:not(:last-child)');
            DrawManagement.elements.$lineItemCategoriesContainer.on('click', '.editable-line-item td:not(:last-child)', function(e) {
                if ($(e.target).closest('.note-container').length && !$(e.target).closest('.description-btn').length
                    && DrawManagement.currentTemplateData.status !== 'pending') {
                    return true;
                }

                e.preventDefault();
                e.stopPropagation();
                return false;
            });
        }

        if (!self.hasPermission('allowBorrowersDeleteLineItems')) {
            $('.remove-line-item-btn').remove();
        }

        if (!self.hasPermission('allowBorrowersAddEditCategories') && !self.hasPermission('allowBorrowersDeleteCategories')) {
            DrawManagement.elements.$saveCatBtn.off('click').on('click', function(e) {
                e.preventDefault();
                toastrNotification('You do not have permission to modify categories.', 'warning');
                return false;
            });
            DrawManagement.elements.$saveCatBtn.remove();
        }

        if (DrawManagement.currentTemplateData.status === 'pending') {
            DrawManagement.elements.$lineItemCategoriesContainer.find('input[type="number"]').prop('disabled', true);
            DrawManagement.elements.$lineItemCategoriesContainer.find('.col-action').remove();
            DrawManagement.elements.$saveLineItemsBtn.remove();
            $('.draft-info').remove();
        }

        if (self.hasPermission('allowBorrowersSOWRevisions') && self.hasPermission('allowBorrowersExceedFinancedRehabCostOnRevision')) {
            DrawManagement.config.validateRehabCostExceeded = true;
        }
    },

    initEventHandlers: function() {
        const self = this;
        this.initNotesModal();

        DrawManagement.elements.$lineItemCategoriesContainer.on('input', 'input[name="completedPercent"], input[name="completedAmount"]', function() {
            DrawManagement.config.lineItemsModified = true;

            const $input = $(this);
            let value = parseFloat($input.val()) || 0;

            if (value < 0) {
                $input.val('');
                return;
            }

            // Round to 2 decimal places
            value = Math.round(value * 100) / 100;

            const fieldName = $input.attr('name');
            const $row = $input.closest('tr');
            const cost = parseFloat($row.find('input[name="cost"]').val()) || 0;

            if (fieldName === 'completedAmount') {
                if (value > cost) {
                    value = cost;
                    $input.val(value);
                }

                const percentage = cost > 0 ? Math.round((value / cost) * 100) : 0;
                if (percentage > 0) {
                    $row.find('input[name="completedPercent"]').val(percentage);
                } else {
                    $row.find('input[name="completedPercent"]').val('');
                }
            }
            else if (fieldName === 'completedPercent') {
                if (value > 100) {
                    value = 100;
                    $input.val(value);
                }

                const amount = Math.round((value / 100) * cost * 100) / 100;
                if (amount > 0) {
                    $row.find('input[name="completedAmount"]').val(amount);
                } else {
                    $row.find('input[name="completedAmount"]').val('');
                }
            }

            $input.val(value);
        });

        DrawManagement.elements.$lineItemCategoriesContainer.on('change', 'input[name="cost"]', function() {
            const $input = $(this);
            const $row = $input.closest('tr');
            const completedAmount = parseFloat($row.find('input[name="completedAmount"]').val()) || 0;
            let value = parseFloat($input.val()) || 0;

            if (value < completedAmount) {
                value = completedAmount;
                $input.val(value);
            }

            const percent = value > 0 ? Math.round((completedAmount / value) * 100) : 0;
            if (percent > 0) {
                $row.find('input[name="completedPercent"]').val(percent);
            } else {
                $row.find('input[name="completedPercent"]').val('');
            }
        });

        self.initNotesPopover('.note-btn');
        self.initCancelRequestButton();
    },

    initNotesModal: function() {
        let currentNoteBtn;

        $('#noteModal').on('show.bs.modal', function (event) {
            currentNoteBtn = $(event.relatedTarget);
            const noteText = currentNoteBtn.data('note') || '';
            $('#noteTextarea').val(noteText);
        });

        $('#saveNoteBtn').on('click', function () {
            const updatedNote = $('#noteTextarea').val();
            currentNoteBtn.data('note', updatedNote);
            currentNoteBtn.attr('data-note', updatedNote);
            currentNoteBtn.find('i').removeClass('text-muted').addClass('text-primary');
            currentNoteBtn.find('i').attr('data-original-title', updatedNote);
            $('#noteModal').modal('hide');

            setTimeout(function() {
                $('.modal-backdrop').remove();
                $('body').removeClass('modal-open');
            }, 150);

            DrawManagement.config.lineItemsModified = true;
        });
    },

    initNotesPopover: function() {
        $('.tooltipClass').tooltip(
            {
                boundary: 'window'
            }
        );
    },

    renderLineItemRowsUI: function(lineItems, $tbodyElement) {
        const rowTemplate = document.getElementById('line-item-row-template');
        if (!rowTemplate) {
            console.error("line-item-row-template not found!");
            return;
        }

        if (lineItems && lineItems.length > 0) {
            lineItems.forEach(item => {
                const clone = rowTemplate.content.cloneNode(true);
                const $row = $(clone).find('tr');
                $row.attr('data-line-item-id', item.id);

                $row.find('.line-item-name-display').text(item.name);
                if(DrawManagement.currentTemplateData.status === 'rejected' && item.rejectReason !== ''){
                    $row.css('background-color', '#f2dede');
                    $row.css('border-left', '2px solid red');
                }
                $row.find('.line-item-name-input').val(item.name);
                $row.find('.line-item-name-display').attr('title', item.description);
                $row.find('.line-item-description-input').val(item.description);
                $row.find('.line-item-disbursed-amount').val(item.disbursedAmount);

                $row.find('input[name="cost"]').val(item.cost || '');
                $row.find('input[name="completedAmount"]').val(item.completedAmount || '');
                $row.find('input[name="completedPercent"]').val(item.completedPercent || '');

                const popoverElems = $row.find('.note-btn:not(.lender-notes)');

                popoverElems.each(function() {
                    const $popoverElem = $(this);
                    const isDescriptionBtn = $popoverElem.hasClass('description-btn');
                    const note = isDescriptionBtn ? item.description : item.notes;
                    if (note) {
                        $popoverElem.data('note', note);
                        $popoverElem.attr('data-note', note);
                        $popoverElem.find('i').removeClass('text-muted').addClass('text-primary');
                        $popoverElem.find('i').attr('data-original-title', note);
                    } else {
                        $popoverElem.data('note', '');
                    }
                });

                let lenderNotes = item.rejectReason !== '' ? item.lenderNotes + '\n\nRejection Reason:\n' + item.rejectReason : item.lenderNotes;
                $row.find('.lender-notes').data('note', lenderNotes.trim());
                $row.find('.lender-notes').attr('data-note', lenderNotes.trim());
                $row.find('.lender-notes').data('original-note', item.lenderNotes.trim() || '');
                $row.find('.lender-notes').find('i').attr('data-original-title', lenderNotes.trim());
                if (lenderNotes.trim()) {
                    $row.find('.lender-notes').find('i').removeClass('text-muted').addClass('text-primary');
                }

                $tbodyElement.append($row);
                this.initNotesPopover();
            });

            if(window.budgetSidebar !== undefined) {
                window.budgetSidebar.refresh();
            }
        }
    },

    getPercentageColor: function(percentage) {
        const p = Math.max(0, Math.min(100, percentage));

        if (p < 25) {
            return 'bg-danger';
        } else if (p < 50) {
            return 'bg-info';
        } else if (p < 75) {
            return 'bg-primary';
        } else {
            return 'bg-success';
        }
    },

    initCancelRequestButton: function() {
        $('#btnCancel').on('click', function(e) {
            e.preventDefault();
            DrawManagement.borrower.cancelRequest();
        });
    },

    cancelRequest: function() {
        $.confirm({
            icon: 'fa fa-warning',
            closeIcon: true,
            title: 'Confirm',
            content: 'Are you sure you want to cancel this request?',
            type: 'red',
            backgroundDismiss: true,
            buttons: {
                yes: {
                    text: 'Yes',
                    action: function () {
                        DrawManagement.borrower.sendCancelRequest();
                    }
                },
                cancel: {
                    text: 'Cancel',
                    action: function () {
                    }
                },
            },
            onClose: function () {
            },
        });
    },

    sendCancelRequest: function() {
        const cancelData = {
            type: $('#btnCancel').data('type'),
            LMRId: DrawManagement.config.LMRId,
        };

        drawManagementApi.cancelRequest(cancelData)
            .then(response => {
                if (response.success) {
                    toastrNotification('Request canceled successfully', 'success');
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    toastrNotification('Failed to cancel request: ' + (response.message || 'Unknown error'), 'error');
                }
            })
            .catch(error => {
                toastrNotification('Failed to cancel request: ' + error.message, 'error');
            });
    }
};

